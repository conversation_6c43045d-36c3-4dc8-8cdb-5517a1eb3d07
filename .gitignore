# ─── GLOBAL GITIGNORE FOR FULL-<PERSON><PERSON><PERSON>O ───

# === Node.js / React ===
node_modules/
.env
.env.*.local
dist/
build/
.vite/
.cache/
coverage/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# === .NET / C# ===
bin/
obj/
*.user
*.suo
*.userosscache
*.sln.docstates
.vscode/
*.ncrunch*
_ReSharper*/
*.vs/
*.dbmdl
*.dbproj.schemaview
*.pdb

# === macOS / Linux / Windows system junk ===
.DS_Store
Thumbs.db
ehthumbs.db
Icon?
desktop.ini

# === Git ===
*.orig
*.swp
*.swo
*.bak

# === Logs ===
logs/
*.log
