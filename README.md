# 🧪 Full-Stack Evaluator – Technical Exam

Welcome to the technical evaluation!

This monorepo contains both the **backend (.NET 9 Web API)** and **frontend (React)** projects. Your task is to build and/or fix key parts of this intentionally incomplete system. Expect quirks. Think like a dev in the wild.

---

## ⏱️ Time Limit

**2 to 3 hours**  
Treat this like a timed test. Don’t overthink it. Show your best work in that window.

---

## 🎯 Objectives

- ✅ Connect frontend to the existing API
- 🔧 Implement or complete missing backend logic
- 🔄 Handle real-world scenarios (partial data, errors, state)
- 💅 Code should be clean, structured, and readable
- 📦 Commit regularly — **no one big fat commit**

---

## 📦 Stack Overview

### Backend

- .NET 9 Web API
- Entity Framework Core
- PostgreSQL
- Swagger docs

### Frontend

- React + Axios
- Redux Toolkit (if present)
- Vite (dev server)
- Styled however you like (no CSS wars)

---

✅ Submission Guidelines
- Push your code to a public GitHub repo
- Include a short write-up in your commit messages or a NOTES.md file:
- What you implemented
- What’s missing (if any)
- How to test your changes

Good luck. Build smart. Code loud. 💻🔥