# 🧪 React Task Evaluator Frontend

⏰ **Estimated Time**: 1-2 hours  
🔧 **Tech Stack**:
- [React 18+](https://reactjs.org/)
- [Vite](https://vitejs.dev/)
- [Axios](https://axios-http.com/)
- Your choice of component styling (<PERSON><PERSON>wind, MUI, plain CSS, go wild)

## 🎯 Objectives

- ✅ Connect to a RESTful API (with some "quirks") – see: [.NET Task Evaluator API](https://github.com/phia-digiteer/dotnet-task-evaluator)
- 🛠️ Implement features with partial or ambiguous requirements  
- ⚠️ Identify places where API usage is unsafe or incomplete  
- 🧠 Show thought process via commits, comments, or UI choices  
- 📦 Handle API failures gracefully  
- 🚀 Add features you think are missing or underexplored


### 📦 Guidelines

- This isn't about pixel-perfect UI. Logic > Looks.
- **Commit often** so we can follow your thought process.
- Leave `TODO` or `FIXME` comments if something’s unclear or unfinished.
- You're free to use any libs—just be transparent.
- Questions? Clarify assumptions in your commits or a note in the README.